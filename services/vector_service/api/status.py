"""
状态查询API
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import traceback

from shared.utils.logger import get_logger
from services.vector_service.models.responses import StatusResponse
from services.vector_service.core.vector_manager import VectorManager
from services.vector_service.core.embeddings import BGEEmbeddingService

logger = get_logger(__name__)

router = APIRouter(prefix="/status", tags=["状态查询"])


def get_vector_manager() -> VectorManager:
    """获取向量管理器依赖"""
    from services.vector_service.main import get_vector_manager
    return get_vector_manager()


def get_embedding_service() -> BGEEmbeddingService:
    """获取嵌入服务依赖"""
    from services.vector_service.main import get_embedding_service
    return get_embedding_service()


@router.get("/health")
async def health_status(
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取系统健康状态
    """
    try:
        health_check = vector_manager.health_check()
        
        # 计算整体健康状态
        overall_healthy = all(health_check.values())
        
        return {
            "status": "healthy" if overall_healthy else "degraded",
            "components": health_check,
            "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/memory")
async def memory_status(
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """
    获取GPU显存使用状态
    """
    try:
        if hasattr(embedding_service, 'get_memory_info'):
            memory_info = embedding_service.get_memory_info()
            
            # 添加状态评估
            memory_status = "healthy"
            if memory_info.get("cuda_available", False):
                usage_percent = memory_info.get("memory_usage_percent", 0)
                if usage_percent > 90:
                    memory_status = "critical"
                elif usage_percent > 75:
                    memory_status = "warning"
            
            return {
                "status": memory_status,
                "memory_info": memory_info,
                "recommendations": _get_memory_recommendations(memory_info)
            }
        else:
            return {
                "status": "unavailable",
                "message": "Memory monitoring not supported",
                "memory_info": {},
                "recommendations": []
            }
            
    except Exception as e:
        logger.error(f"Memory status check failed: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Memory status check failed: {str(e)}"
        )


@router.post("/memory/cleanup")
async def cleanup_memory(
    force: bool = False,
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    手动清理GPU显存
    
    Args:
        force: 是否强制清理（包括模型重载）
    """
    try:
        result = vector_manager.manual_cleanup_memory(force=force)
        
        if result["success"]:
            return {
                "status": "success",
                "message": result["message"],
                "memory_before": result.get("memory_before"),
                "memory_after": result.get("memory_after"),
                "force_cleanup": force
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Memory cleanup failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Memory cleanup failed: {str(e)}"
        )


@router.get("/files")
async def list_files_status(
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取所有文件的状态
    """
    try:
        files = vector_manager.list_files()
        
        return {
            "total_files": len(files),
            "files": [
                {
                    "file_code": file_info.file_code,
                    "filename": file_info.filename,
                    "status": file_info.status.value,
                    "document_count": file_info.document_count,
                    "uploaded_at": file_info.uploaded_at.isoformat() if file_info.uploaded_at else None,
                    "updated_at": file_info.updated_at.isoformat() if file_info.updated_at else None,
                }
                for file_info in files
            ]
        }
    except Exception as e:
        logger.error(f"List files failed: {e}")
        raise HTTPException(status_code=500, detail=f"List files failed: {str(e)}")


@router.get("/files/{file_code}")
async def get_file_status(
    file_code: str,
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取特定文件的状态
    """
    try:
        file_info = vector_manager.get_file_status(file_code)
        
        if not file_info:
            raise HTTPException(status_code=404, detail=f"File {file_code} not found")
        
        return {
            "file_code": file_info.file_code,
            "filename": file_info.filename,
            "status": file_info.status.value,
            "document_count": file_info.document_count,
            "uploaded_at": file_info.uploaded_at.isoformat() if file_info.uploaded_at else None,
            "updated_at": file_info.updated_at.isoformat() if file_info.updated_at else None,
            "file_size": file_info.file_size,
            "file_type": file_info.file_type,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get file status failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get file status failed: {str(e)}")


@router.get("/memory/leak-check")
async def check_memory_leak(
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """
    检查潜在的显存泄漏
    """
    try:
        if hasattr(embedding_service, 'check_memory_leak'):
            leak_check_result = embedding_service.check_memory_leak()
            
            return {
                "status": "success",
                "leak_check": leak_check_result,
                "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
            }
        else:
            return {
                "status": "unavailable",
                "message": "Memory leak detection not supported",
                "leak_check": {}
            }
            
    except Exception as e:
        logger.error(f"Memory leak check failed: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Memory leak check failed: {str(e)}"
        )


@router.get("/cache")
async def get_cache_status(
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """获取嵌入缓存状态"""
    try:
        if hasattr(embedding_service, 'get_cache_stats'):
            cache_stats = embedding_service.get_cache_stats()
            
            # 评估缓存状态
            cache_status = "healthy"
            if cache_stats['memory_usage_percent'] > 90:
                cache_status = "critical"
            elif cache_stats['memory_usage_percent'] > 75:
                cache_status = "warning"
            
            return {
                "status": cache_status,
                "cache_stats": cache_stats,
                "recommendations": _get_cache_recommendations(cache_stats)
            }
        else:
            return {
                "status": "unavailable",
                "message": "Cache statistics not supported",
                "cache_stats": {},
                "recommendations": []
            }
            
    except Exception as e:
        logger.error(f"Cache status check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache status check failed: {str(e)}"
        )


@router.get("/vector-stores")
async def get_vector_stores_status(
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """获取向量存储实例状态"""
    try:
        if hasattr(vector_manager, 'get_vector_store_stats'):
            vector_store_stats = vector_manager.get_vector_store_stats()
            
            # 评估向量存储状态
            vs_status = "healthy"
            if vector_store_stats['instances_usage_percent'] > 90:
                vs_status = "critical"
            elif vector_store_stats['instances_usage_percent'] > 75:
                vs_status = "warning"
            
            return {
                "status": vs_status,
                "vector_store_stats": vector_store_stats,
                "recommendations": _get_vector_store_recommendations(vector_store_stats)
            }
        else:
            return {
                "status": "unavailable",
                "message": "Vector store statistics not supported",
                "vector_store_stats": {},
                "recommendations": []
            }
            
    except Exception as e:
        logger.error(f"Vector store status check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Vector store status check failed: {str(e)}"
        )


@router.post("/cache/cleanup")
async def cleanup_cache(
    force: bool = False,
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """手动清理嵌入缓存"""
    try:
        stats_before = embedding_service.get_cache_stats() if hasattr(embedding_service, 'get_cache_stats') else {}
        
        if force:
            # 强制清理所有缓存
            if hasattr(embedding_service, 'embedding_cache'):
                cache_count = len(embedding_service.embedding_cache)
                embedding_service.embedding_cache.clear()
                if hasattr(embedding_service, 'cache_access_times'):
                    embedding_service.cache_access_times.clear()
                embedding_service.cache_memory_usage = 0
                message = f"强制清理了 {cache_count} 个缓存条目"
            else:
                message = "缓存不可用"
        else:
            # 智能清理
            if hasattr(embedding_service, '_cleanup_expired_cache'):
                embedding_service._cleanup_expired_cache()
                message = "清理了过期缓存"
            else:
                message = "缓存清理不支持"
        
        stats_after = embedding_service.get_cache_stats() if hasattr(embedding_service, 'get_cache_stats') else {}
        
        return {
            "status": "success",
            "message": message,
            "stats_before": stats_before,
            "stats_after": stats_after,
            "force_cleanup": force
        }
        
    except Exception as e:
        logger.error(f"Cache cleanup failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache cleanup failed: {str(e)}"
        )


@router.post("/vector-stores/cleanup")
async def cleanup_vector_stores(
    force: bool = False,
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """手动清理向量存储实例"""
    try:
        if hasattr(vector_manager, 'manual_cleanup_vector_stores'):
            result = vector_manager.manual_cleanup_vector_stores(force=force)
            
            return {
                "status": "success" if result["success"] else "failed",
                "message": result["message"],
                "instances_before": result.get("instances_before", 0),
                "instances_after": result.get("instances_after", 0),
                "force_cleanup": force
            }
        else:
            return {
                "status": "unavailable",
                "message": "Vector store cleanup not supported"
            }
            
    except Exception as e:
        logger.error(f"Vector store cleanup failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Vector store cleanup failed: {str(e)}"
        )


def _get_memory_recommendations(memory_info: Dict[str, Any]) -> list:
    """
    根据显存使用情况提供建议
    """
    recommendations = []
    
    if not memory_info.get("cuda_available", False):
        # CPU模式的建议
        if memory_info.get("mode") == "CPU":
            ram_percent = memory_info.get("system_ram_percent", 0)
            if ram_percent > 90:
                recommendations.extend([
                    "系统内存使用率过高，建议清理内存",
                    "考虑重启服务或减小批处理大小"
                ])
            elif ram_percent > 75:
                recommendations.extend([
                    "系统内存使用率较高，建议定期清理",
                    "监控后续任务的内存使用"
                ])
            else:
                recommendations.append("CPU模式运行正常，内存使用合理")
        else:
            recommendations.append("使用CPU模式，无需GPU显存管理")
        return recommendations
    
    # GPU模式的建议
    usage_percent = memory_info.get("memory_usage_percent", 0)
    
    if usage_percent > 90:
        recommendations.extend([
            "显存使用率过高，建议立即清理",
            "考虑使用force=true进行强制清理",
            "检查是否有显存泄漏"
        ])
    elif usage_percent > 75:
        recommendations.extend([
            "显存使用率较高，建议定期清理",
            "监控后续向量化任务的显存使用"
        ])
    elif usage_percent < 10:
        recommendations.append("显存使用率正常，系统运行良好")
    
    return recommendations


def _get_cache_recommendations(cache_stats: Dict[str, Any]) -> list:
    """根据缓存状态提供建议"""
    recommendations = []
    
    if not cache_stats.get("enabled", False):
        recommendations.append("缓存已禁用，建议启用以提高性能")
        return recommendations
    
    memory_percent = cache_stats.get("memory_usage_percent", 0)
    entries_percent = cache_stats.get("entries_usage_percent", 0)
    
    if memory_percent > 90:
        recommendations.extend([
            "缓存内存使用率过高，建议立即清理",
            "考虑减小缓存大小限制或增加清理频率"
        ])
    elif memory_percent > 75:
        recommendations.extend([
            "缓存内存使用率较高，建议定期清理",
            "监控后续缓存增长"
        ])
    
    if entries_percent > 90:
        recommendations.extend([
            "缓存条目数过多，建议清理",
            "考虑减小最大条目数限制"
        ])
    
    if memory_percent < 10 and entries_percent < 10:
        recommendations.append("缓存使用率正常，系统运行良好")
    
    return recommendations


def _get_vector_store_recommendations(vs_stats: Dict[str, Any]) -> list:
    """根据向量存储状态提供建议"""
    recommendations = []
    
    instances_percent = vs_stats.get("instances_usage_percent", 0)
    total_instances = vs_stats.get("total_instances", 0)
    
    if instances_percent > 90:
        recommendations.extend([
            "向量存储实例数过多，建议立即清理",
            "考虑增加实例生存时间或减小最大实例数"
        ])
    elif instances_percent > 75:
        recommendations.extend([
            "向量存储实例数较多，建议定期清理",
            "监控实例使用模式"
        ])
    
    if total_instances == 0:
        recommendations.append("当前无活跃的向量存储实例")
    elif instances_percent < 25:
        recommendations.append("向量存储实例使用率正常")
    
    return recommendations
