"""
BGE-M3嵌入服务
基于原有的embeddings.py进行适配，保持完全一致的实现
"""

import os
import time
import hashlib
import pickle
import torch
import gc
import threading
import atexit
from typing import List, Optional, Dict, Any
from pathlib import Path
import numpy as np
from sklearn.preprocessing import normalize
from FlagEmbedding import BGEM3FlagModel

from shared.utils.logger import get_logger
from services.vector_service.config import config

logger = get_logger(__name__)


def batch_process(items: List[Any], batch_size: int):
    """批处理工具函数"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]


def get_optimal_device(device: str = "auto") -> str:
    """获取最优设备"""
    if device == "auto":
        if torch.cuda.is_available():
            return "cuda"
        else:
            return "cpu"
    return device


def recommend_device_settings(model_size_gb: float = 2.3) -> Dict[str, Any]:
    """推荐设备设置"""
    settings = {
        "use_fp16": torch.cuda.is_available(),
        "batch_size": 32,
        "warnings": []
    }

    if torch.cuda.is_available():
        # GPU可用时的推荐设置
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        if gpu_memory < 8:
            settings["batch_size"] = 16
            settings["warnings"].append("GPU内存较小，建议使用较小的batch_size")
    else:
        settings["use_fp16"] = False
        settings["batch_size"] = 16
        settings["warnings"].append("未检测到GPU，使用CPU模式")

    return settings


class BGEEmbeddingService:
    """BGE-M3嵌入服务 - 基于原有BGEM3Embeddings实现"""

    def __init__(
        self,
        model_name: str = None,
        cache_dir: Optional[str] = None,
        batch_size: int = None,
        max_length: int = None,
        device: str = "auto",
        enable_cache: bool = True,
        cache_ttl: int = 86400,  # 24小时
        normalize_embeddings: bool = True,
        auto_cleanup: bool = True,  # 自动清理开关
        memory_monitor: bool = True,  # 显存监控开关
        model_idle_timeout: int = 3600,  # 模型空闲超时时间（秒）
        model_check_interval: int = 300,  # 模型检查间隔（秒）
        **kwargs,
    ):
        """
        初始化BGE-M3嵌入服务

        Args:
            model_name: 模型名称
            cache_dir: 模型缓存目录
            batch_size: 批处理大小
            max_length: 最大序列长度
            device: 设备 (cpu, cuda, auto)
            enable_cache: 是否启用嵌入缓存
            cache_ttl: 缓存过期时间（秒）
            normalize_embeddings: 是否标准化嵌入向量
            auto_cleanup: 是否启用自动显存清理
            memory_monitor: 是否启用显存监控
        """
        # 使用配置或默认值
        self.model_name = model_name or config.model_name
        self.cache_dir = Path(cache_dir) if cache_dir else Path(config.model_cache_dir)
        self.batch_size = batch_size or config.batch_size
        self.max_length = max_length or config.max_length
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self.normalize_embeddings = normalize_embeddings
        
        # GPU显存管理配置
        self.auto_cleanup = auto_cleanup if auto_cleanup is not None else config.auto_cleanup
        self.memory_monitor = memory_monitor if memory_monitor is not None else config.memory_monitor
        self.cleanup_threshold = config.cleanup_threshold
        self.batch_cleanup_interval = config.batch_cleanup_interval

        # 使用统一的设备选择逻辑
        self.device = get_optimal_device(device)

        # 获取设备推荐设置
        device_recommendations = recommend_device_settings(model_size_gb=2.3)
        self.use_fp16 = device_recommendations.get("use_fp16", False)

        # 如果用户没有指定批处理大小，使用推荐值
        if batch_size is None:
            self.batch_size = device_recommendations.get("batch_size", 32)

        # 输出设备推荐警告
        for warning in device_recommendations.get("warnings", []):
            logger.warning(warning)

        # 初始化模型（懒加载）
        self.model = None
        self._model_last_used = 0  # 模型最后使用时间
        self._model_usage_count = 0  # 模型使用次数
        self.model_idle_timeout = model_idle_timeout  # 模型空闲超时时间
        self.lazy_loading = True  # 启用懒加载
        
        if not self.lazy_loading:
            self._load_model()

        # 初始化缓存（增强版）
        self.embedding_cache = {}
        self.cache_dir_path = self.cache_dir / "embedding_cache"
        self.cache_access_times = {}  # LRU跟踪
        self.cache_memory_usage = 0  # 缓存内存使用跟踪
        
        # 缓存限制配置
        self.max_cache_entries = 10000  # 最大缓存条目数
        self.max_cache_memory_mb = 500  # 最大缓存内存（MB）
        self.cache_cleanup_threshold = 0.8  # 清理阈值
        
        if self.enable_cache:
            self.cache_dir_path.mkdir(parents=True, exist_ok=True)
            self._load_cache()

        # 周期性清理统计（优化）
        self._cleanup_counter = 0
        self._periodic_cleanup_interval = 50  # 减少到每50次嵌入操作进行清理
        self._last_cache_cleanup = time.time()

        logger.info(f"BGE-M3 embedding service initialized on {self.device}")
        if self.memory_monitor:
            self.log_memory_usage("初始化完成")
        
        # 模型空闲检查器属性
        self._model_idle_checker_thread = None
        self._model_idle_checker_stop_event = threading.Event()
        self.model_check_interval = model_check_interval  # 模型检查间隔
        self._model_lock = threading.RLock()  # 模型操作锁

        # 启动模型空闲检查任务
        if self.lazy_loading:
            self._start_model_idle_checker()

        # 注册清理函数
        atexit.register(self.cleanup)

    def _load_model(self):
        """加载BGE-M3嵌入模型 - 仅从本地路径加载"""
        try:
            logger.info(f"正在加载 BGE-M3 嵌入模型")
            logger.info(f"本地模型路径: {self.model_name}")
            logger.info(f"设备: {self.device}")
            logger.info(f"缓存目录: {self.cache_dir}")

            # 验证本地模型路径存在
            model_path = Path(self.model_name)
            if not model_path.exists():
                raise FileNotFoundError(f"本地模型路径不存在: {model_path}")
            
            # 检查必要的模型文件
            required_files = ["config.json", "model.safetensors", "tokenizer.json"]
            missing_files = []
            for file_name in required_files:
                if not (model_path / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                raise FileNotFoundError(f"缺少必要的模型文件: {missing_files}")

            logger.info(f"✅ 本地模型文件验证通过")

            # 加载BGE-M3模型，使用推荐的设置
            self.model = BGEM3FlagModel(
                str(model_path), use_fp16=self.use_fp16, device=self.device
            )

            logger.info("✅ BGE-M3 模型加载成功")
            logger.info("模型维度: 1024")  # BGE-M3固定维度
            logger.info(f"最大序列长度: {self.max_length}")
            logger.info(f"使用半精度: {self.use_fp16}")
            logger.info(f"设备: {self.device}")

        except Exception as e:
            logger.error(f"❌ BGE-M3 模型加载失败: {e}")

            # 模型加载失败时清理可能的部分加载状态
            if hasattr(self, 'model'):
                self.model = None
                
            # 清理可能占用的显存
            gc.collect()
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()

            # 提供本地模型加载的故障排除建议
            error_msg = f"无法从本地路径加载 BGE-M3 嵌入模型: {e}\n\n"
            error_msg += "故障排除建议:\n"
            error_msg += "1. 确保已安装 FlagEmbedding 库: pip install FlagEmbedding\n"
            error_msg += "2. 检查本地模型路径是否正确\n"
            error_msg += "3. 验证模型文件完整性（config.json, model.safetensors, tokenizer.json）\n"
            error_msg += "4. 检查磁盘空间，BGE-M3 需要约 2.3GB 空间\n"
            error_msg += f"5. 当前模型路径: {self.model_name}\n"

            if "CUDA" in str(e) or "cuda" in str(e):
                error_msg += (
                    "6. CUDA 相关错误：尝试设置 device='cpu' 或检查 CUDA 安装\n"
                )

            if "memory" in str(e).lower():
                error_msg += (
                    "6. 内存不足：尝试设置 use_fp16=True 或使用更小的 batch_size\n"
                )

            raise RuntimeError(error_msg)

    def _get_cache_key(self, text: str) -> str:
        """生成BGE-M3特定的缓存键"""
        # 使用文本内容、模型名称、序列长度和标准化设置生成唯一键
        content = f"BGE-M3:{self.model_name}:{self.max_length}:{self.normalize_embeddings}:{text}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()

    def _load_cache(self):
        """加载缓存"""
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                with open(cache_file, "rb") as f:
                    cache_data = pickle.load(f)

                # 检查缓存是否过期
                current_time = time.time()
                valid_cache = {}

                for key, (embedding, timestamp) in cache_data.items():
                    if current_time - timestamp < self.cache_ttl:
                        valid_cache[key] = (embedding, timestamp)

                self.embedding_cache = valid_cache
                logger.info(f"加载嵌入缓存: {len(valid_cache)} 条记录")

        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            self.embedding_cache = {}

    def _save_cache(self):
        """保存缓存"""
        if not self.enable_cache:
            return

        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            with open(cache_file, "wb") as f:
                pickle.dump(self.embedding_cache, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _get_cached_embedding(self, text: str) -> Optional[np.ndarray]:
        """获取缓存的嵌入（增强版本）"""
        if not self.enable_cache:
            return None

        cache_key = self._get_cache_key(text)
        if cache_key in self.embedding_cache:
            cache_item = self.embedding_cache[cache_key]
            
            # 兼容旧格式和新格式
            if isinstance(cache_item, tuple):
                embedding, timestamp = cache_item
                size = embedding.nbytes if hasattr(embedding, 'nbytes') else len(embedding) * 4
            else:
                embedding = cache_item["embedding"]
                timestamp = cache_item["timestamp"]
                size = cache_item.get("size", 0)

            # 检查是否过期
            current_time = time.time()
            if current_time - timestamp < self.cache_ttl:
                # 更新访问时间（LRU）
                self.cache_access_times[cache_key] = current_time
                return embedding
            else:
                # 删除过期缓存
                del self.embedding_cache[cache_key]
                if cache_key in self.cache_access_times:
                    del self.cache_access_times[cache_key]
                self.cache_memory_usage -= size

        return None

    def _cache_embedding(self, text: str, embedding: np.ndarray):
        """缓存嵌入（增强版本）"""
        if not self.enable_cache:
            return

        cache_key = self._get_cache_key(text)
        current_time = time.time()
        
        # 计算新条目的内存使用
        embedding_size = embedding.nbytes if hasattr(embedding, 'nbytes') else len(embedding) * 4
        
        # 检查是否需要清理缓存
        if (len(self.embedding_cache) >= self.max_cache_entries * self.cache_cleanup_threshold or
            self.cache_memory_usage >= self.max_cache_memory_mb * 1024 * 1024 * self.cache_cleanup_threshold):
            self._cleanup_cache_by_pressure()
        
        # 添加/更新缓存
        if cache_key in self.embedding_cache:
            # 更新现有条目
            old_item = self.embedding_cache[cache_key]
            old_size = getattr(old_item, 'size', 0) if hasattr(old_item, 'size') else embedding_size
            self.cache_memory_usage = self.cache_memory_usage - old_size + embedding_size
        else:
            # 新条目
            self.cache_memory_usage += embedding_size
        
        # 存储为字典格式，便于扩展
        self.embedding_cache[cache_key] = {
            "embedding": embedding,
            "timestamp": current_time,
            "size": embedding_size
        }
        self.cache_access_times[cache_key] = current_time

    def _cleanup_cache_by_pressure(self):
        """基于内存压力清理缓存"""
        if not self.enable_cache:
            return
            
        logger.debug(f"🔄 开始缓存压力清理 - 当前条目数: {len(self.embedding_cache)}, 内存使用: {self.cache_memory_usage / 1024 / 1024:.1f}MB")
        
        # 计算需要清理的目标
        target_entries = int(self.max_cache_entries * 0.7)  # 清理到70%
        target_memory = int(self.max_cache_memory_mb * 1024 * 1024 * 0.7)  # 清理到70%
        
        # 按访问时间排序（LRU）
        sorted_keys = sorted(
            self.cache_access_times.keys(),
            key=lambda k: self.cache_access_times[k]
        )
        
        keys_to_remove = []
        memory_freed = 0
        
        # 清理最少使用的条目
        for cache_key in sorted_keys:
            if (len(self.embedding_cache) - len(keys_to_remove) <= target_entries and
                self.cache_memory_usage - memory_freed <= target_memory):
                break
                
            if cache_key in self.embedding_cache:
                cache_item = self.embedding_cache[cache_key]
                if isinstance(cache_item, dict):
                    memory_freed += cache_item.get("size", 0)
                else:
                    # 旧格式，估算大小
                    memory_freed += 1024 * 4  # 估算1024维 * 4字节
                keys_to_remove.append(cache_key)
        
        # 执行清理
        for cache_key in keys_to_remove:
            if cache_key in self.embedding_cache:
                del self.embedding_cache[cache_key]
            if cache_key in self.cache_access_times:
                del self.cache_access_times[cache_key]
        
        self.cache_memory_usage -= memory_freed
        
        logger.debug(f"✅ 缓存压力清理完成 - 清理了 {len(keys_to_remove)} 个条目, 释放内存: {memory_freed / 1024 / 1024:.1f}MB")

    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        if not self.enable_cache:
            return
            
        current_time = time.time()
        expired_keys = []
        memory_freed = 0
        
        for cache_key, cache_item in self.embedding_cache.items():
            if isinstance(cache_item, tuple):
                _, timestamp = cache_item
                size = 1024 * 4  # 估算大小
            else:
                timestamp = cache_item["timestamp"]
                size = cache_item.get("size", 0)
            
            if current_time - timestamp >= self.cache_ttl:
                expired_keys.append(cache_key)
                memory_freed += size
        
        # 执行清理
        for cache_key in expired_keys:
            if cache_key in self.embedding_cache:
                del self.embedding_cache[cache_key]
            if cache_key in self.cache_access_times:
                del self.cache_access_times[cache_key]
        
        self.cache_memory_usage -= memory_freed
        
        if expired_keys:
            logger.debug(f"✅ 清理了 {len(expired_keys)} 个过期缓存条目, 释放内存: {memory_freed / 1024 / 1024:.1f}MB")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "enabled": self.enable_cache,
            "total_entries": len(self.embedding_cache),
            "max_entries": self.max_cache_entries,
            "memory_usage_mb": self.cache_memory_usage / 1024 / 1024,
            "max_memory_mb": self.max_cache_memory_mb,
            "memory_usage_percent": (self.cache_memory_usage / (self.max_cache_memory_mb * 1024 * 1024)) * 100,
            "entries_usage_percent": (len(self.embedding_cache) / self.max_cache_entries) * 100
        }

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text or not text.strip():
            return ""

        # 基本清理
        text = text.strip()

        # 限制长度（按字符数估算，BGE-M3支持8192 tokens）
        max_chars = self.max_length * 4  # 粗略估算
        if len(text) > max_chars:
            text = text[:max_chars]
            logger.debug(f"Text truncated to {max_chars} characters")

        return text

    def get_memory_info(self) -> Dict[str, Any]:
        """
        获取GPU显存使用信息（高精度版本）
        
        Returns:
            显存信息字典
        """
        memory_info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available()
        }
        
        if torch.cuda.is_available() and self.device == "cuda":
            # ★★★ 新增：先同步确保准确读取 ★★★
            torch.cuda.synchronize()
            
            # 当前设备显存信息
            current_device = torch.cuda.current_device()
            
            # ★★★ 新增：多次读取取平均值提高精度 ★★★
            allocated_values = []
            reserved_values = []
            for _ in range(3):
                allocated_values.append(torch.cuda.memory_allocated(current_device))
                reserved_values.append(torch.cuda.memory_reserved(current_device))
                torch.cuda.synchronize()
            
            allocated = sum(allocated_values) / len(allocated_values) / 1024**3  # GB
            reserved = sum(reserved_values) / len(reserved_values) / 1024**3  # GB
            
            memory_info.update({
                "current_device": current_device,
                "allocated": allocated,
                "reserved": reserved,
                "max_allocated": torch.cuda.max_memory_allocated(current_device) / 1024**3,  # GB
                "max_reserved": torch.cuda.max_memory_reserved(current_device) / 1024**3,  # GB
            })
            
            # 获取设备属性
            device_props = torch.cuda.get_device_properties(current_device)
            memory_info.update({
                "total_memory": device_props.total_memory / 1024**3,  # GB
                "device_name": device_props.name,
                "memory_usage_percent": (allocated / (device_props.total_memory / 1024**3)) * 100
            })
        else:
            # CPU模式或CUDA不可用时的信息（增强版）
            try:
                import psutil
                import os
                
                # 获取系统内存信息
                ram_info = psutil.virtual_memory()
                
                # 获取当前进程内存信息
                current_process = psutil.Process(os.getpid())
                process_memory = current_process.memory_info()
                process_memory_percent = (process_memory.rss / ram_info.total) * 100
                
                # 获取缓存统计
                cache_stats = self.get_cache_stats()
                
                memory_info.update({
                    "mode": "CPU",
                    # 系统级内存信息
                    "system_ram_total": ram_info.total / 1024**3,  # GB
                    "system_ram_available": ram_info.available / 1024**3,  # GB
                    "system_ram_used": ram_info.used / 1024**3,  # GB
                    "system_ram_percent": ram_info.percent,
                    
                    # 进程级内存信息
                    "process_memory_rss": process_memory.rss / 1024**3,  # GB (物理内存)
                    "process_memory_vms": process_memory.vms / 1024**3,  # GB (虚拟内存)
                    "process_memory_percent": process_memory_percent,
                    
                    # 缓存内存信息
                    "cache_memory_mb": cache_stats.get("memory_usage_mb", 0),
                    "cache_entries": cache_stats.get("total_entries", 0),
                    
                    # 兼容性字段
                    "memory_usage_percent": process_memory_percent
                })
                
                # 如果可用，获取更详细的进程内存信息
                try:
                    process_memory_full = current_process.memory_full_info()
                    memory_info.update({
                        "process_memory_uss": process_memory_full.uss / 1024**3,  # GB (独占内存)
                        "process_memory_pss": process_memory_full.pss / 1024**3,  # GB (比例内存)
                    })
                except (AttributeError, psutil.AccessDenied):
                    # 某些系统不支持memory_full_info
                    pass
                    
            except ImportError:
                # 如果psutil不可用，提供基本信息
                memory_info.update({
                    "mode": "CPU",
                    "note": "psutil not available, limited memory info",
                    "memory_usage_percent": 0,  # 兼容性字段
                    "cache_memory_mb": 0,
                    "cache_entries": 0
                })
            except Exception as e:
                # 处理其他异常
                memory_info.update({
                    "mode": "CPU",
                    "error": f"Memory info collection failed: {e}",
                    "memory_usage_percent": 0,  # 兼容性字段
                    "cache_memory_mb": 0,
                    "cache_entries": 0
                })
        
        return memory_info

    def log_memory_usage(self, context: str = ""):
        """
        记录显存使用情况
        
        Args:
            context: 上下文信息
        """
        if not self.memory_monitor:
            return
            
        try:
            memory_info = self.get_memory_info()
            if memory_info["cuda_available"] and self.device == "cuda":
                allocated = memory_info["allocated"]
                total = memory_info["total_memory"]
                usage_percent = memory_info["memory_usage_percent"]
                
                logger.info(
                    f"🔍 GPU显存使用 [{context}]: "
                    f"{allocated:.2f}GB/{total:.2f}GB ({usage_percent:.1f}%)"
                )
                
                # 如果显存使用率过高，发出警告
                if usage_percent > self.cleanup_threshold:
                    logger.warning(
                        f"⚠️  GPU显存使用率过高: {usage_percent:.1f}% (阈值: {self.cleanup_threshold}%), "
                        f"建议进行显存清理"
                    )
            else:
                # CPU模式下的内存监控（增强版）
                if memory_info.get("mode") == "CPU":
                    # 系统级内存信息
                    ram_used = memory_info.get("system_ram_used", 0)
                    ram_total = memory_info.get("system_ram_total", 0)
                    ram_percent = memory_info.get("system_ram_percent", 0)
                    
                    # 进程级内存信息
                    process_rss = memory_info.get("process_memory_rss", 0)
                    process_percent = memory_info.get("process_memory_percent", 0)
                    
                    # 缓存信息
                    cache_mb = memory_info.get("cache_memory_mb", 0)
                    cache_entries = memory_info.get("cache_entries", 0)
                    
                    # 详细日志记录
                    if process_rss > 0:
                        logger.info(
                            f"💻 CPU内存使用 [{context}]: "
                            f"系统: {ram_used:.2f}GB/{ram_total:.2f}GB ({ram_percent:.1f}%) | "
                            f"进程: {process_rss:.2f}GB ({process_percent:.1f}%) | "
                            f"缓存: {cache_mb:.1f}MB ({cache_entries}条目)"
                        )
                    else:
                        # 如果无法获取进程信息，使用简化版本
                        logger.info(
                            f"💻 CPU内存使用 [{context}]: "
                            f"系统: {ram_used:.2f}GB/{ram_total:.2f}GB ({ram_percent:.1f}%) | "
                            f"缓存: {cache_mb:.1f}MB ({cache_entries}条目)"
                        )
                    
                    # 多级内存压力检测
                    critical_issues = []
                    warning_issues = []
                    
                    # 系统内存检测
                    if ram_percent > 90:
                        critical_issues.append(f"系统内存使用率过高: {ram_percent:.1f}%")
                    elif ram_percent > 80:
                        warning_issues.append(f"系统内存使用率较高: {ram_percent:.1f}%")
                    
                    # 进程内存检测
                    if process_percent > 20:  # 单进程占用超过20%系统内存
                        critical_issues.append(f"进程内存占用过高: {process_percent:.1f}%")
                    elif process_percent > 10:
                        warning_issues.append(f"进程内存占用较高: {process_percent:.1f}%")
                    
                    # 缓存内存检测
                    if cache_mb > 1000:  # 缓存超过1GB
                        warning_issues.append(f"缓存占用较高: {cache_mb:.1f}MB")
                    
                    # 处理关键问题
                    if critical_issues:
                        logger.error(f"🚨 内存使用关键问题: {'; '.join(critical_issues)}")
                        if self.auto_cleanup:
                            logger.info("🔄 触发自动清理...")
                            self._cleanup_expired_cache()
                            if cache_mb > 500:
                                self._cleanup_cache_by_pressure()
                    
                    # 处理警告问题
                    elif warning_issues:
                        logger.warning(f"⚠️  内存使用警告: {'; '.join(warning_issues)}")
                        if self.auto_cleanup and cache_mb > 300:
                            logger.debug("🔄 触发预防性缓存清理...")
                            self._cleanup_expired_cache()
                    
                    # 检查内存增长趋势（如果有历史数据）
                    if hasattr(self, '_last_memory_info'):
                        last_process_rss = self._last_memory_info.get("process_memory_rss", 0)
                        if process_rss > last_process_rss * 1.5:  # 内存增长超过50%
                            logger.warning(
                                f"⚠️  进程内存快速增长: "
                                f"{last_process_rss:.2f}GB -> {process_rss:.2f}GB"
                            )
                    
                    # 保存当前内存信息用于趋势分析
                    self._last_memory_info = memory_info
                    
                else:
                    logger.debug(f"💻 CPU模式运行 [{context}]: 设备={self.device}")
        except Exception as e:
            logger.debug(f"显存监控失败: {e}")

    def cleanup_memory(self, force: bool = False):
        """
        清理GPU显存
        
        Args:
            force: 是否强制清理（包括模型）
        """
        try:
            if self.memory_monitor:
                self.log_memory_usage("清理前")
            
            # ★★★ 新增：先同步所有GPU操作 ★★★
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.synchronize()
            
            # 清理Python垃圾回收（适用于所有环境）
            gc.collect()
            
            # 清理CUDA缓存（如果使用CUDA）
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
                # ★★★ 新增：重置峰值内存统计 ★★★
                torch.cuda.reset_max_memory_allocated()
                torch.cuda.synchronize()  # 确保清理操作完成
                logger.debug("✅ CUDA缓存已清理并同步")
            else:
                logger.debug("💻 CPU模式: 执行Python垃圾回收")
                
                # CPU模式专用清理策略
                if self.device == "cpu":
                    # 清理过期缓存
                    self._cleanup_expired_cache()
                    
                    # 检查缓存压力
                    cache_stats = self.get_cache_stats()
                    if (cache_stats['memory_usage_percent'] > 70 or 
                        cache_stats['entries_usage_percent'] > 70):
                        logger.debug("CPU模式: 执行缓存压力清理")
                        self._cleanup_cache_by_pressure()
                    
                    # 额外的垃圾回收轮次（CPU模式更依赖GC）
                    import time as time_module
                    for _ in range(2):
                        gc.collect()
                        time_module.sleep(0.05)
                    
                    logger.debug("✅ CPU模式专用清理完成")
            
            # 强制清理时，清理模型引用
            if force and self.model is not None:
                logger.info("🔄 强制清理模型引用...")
                del self.model
                self.model = None
                gc.collect()
                if torch.cuda.is_available() and self.device == "cuda":
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                logger.info("✅ 模型引用已清理")
            
            # ★★★ 新增：延迟一小段时间确保清理生效 ★★★
            import time as time_module
            time_module.sleep(0.1)  # 100ms延迟确保GPU操作完成
            
            if self.memory_monitor:
                self.log_memory_usage("清理后")
                
        except Exception as e:
            logger.warning(f"显存清理时出现警告: {e}")

    def synchronize_and_cleanup(self):
        """
        同步GPU操作并清理显存
        
        专门用于确保GPU操作完成后进行彻底清理
        """
        try:
            if torch.cuda.is_available() and self.device == "cuda":
                # 强制同步所有GPU操作
                torch.cuda.synchronize()
                logger.debug("🔄 GPU操作已同步")
            
            # 立即清理
            gc.collect()
            
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
                torch.cuda.synchronize()  # 再次同步确保清理完成
                logger.debug("✅ 同步清理完成")
            
        except Exception as e:
            logger.debug(f"同步清理时出现警告: {e}")

    def cleanup(self):
        """
        完整清理资源（服务关闭时调用）
        """
        logger.info("🔄 开始清理BGE嵌入服务资源...")

        try:
            # 停止模型空闲检查器
            self._stop_model_idle_checker()
            # 清理嵌入缓存（增强版）
            if hasattr(self, 'embedding_cache'):
                cache_count = len(self.embedding_cache)
                cache_memory = getattr(self, 'cache_memory_usage', 0)
                
                self.embedding_cache.clear()
                if hasattr(self, 'cache_access_times'):
                    self.cache_access_times.clear()
                self.cache_memory_usage = 0
                
                logger.debug(f"✅ 嵌入缓存已清理 - 清理了 {cache_count} 个条目, 释放内存: {cache_memory / 1024 / 1024:.1f}MB")
            
            # 清理模型
            if hasattr(self, 'model') and self.model is not None:
                del self.model
                self.model = None
                logger.debug("✅ 模型引用已清理")
            
            # 强制清理GPU显存
            self.cleanup_memory(force=True)
            
            # 额外的环境特定清理
            if torch.cuda.is_available() and self.device == "cuda":
                logger.info("✅ BGE嵌入服务资源清理完成 (GPU模式)")
            else:
                logger.info("✅ BGE嵌入服务资源清理完成 (CPU模式)")
            
        except Exception as e:
            logger.warning(f"⚠️  BGE嵌入服务清理时出现警告: {e}")

    def is_model_loaded(self) -> bool:
        """
        检查模型是否已加载
        
        Returns:
            模型是否已加载
        """
        return self.model is not None

    def reload_model(self):
        """
        重新加载模型
        """
        logger.info("🔄 重新加载BGE-M3模型...")
        
        # 先清理现有模型
        if self.model is not None:
            del self.model
            self.model = None
            self.cleanup_memory()
        
        # 重新加载模型
        self._load_model()
        
        if self.memory_monitor:
            self.log_memory_usage("模型重载完成")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表 - 完全基于原有BGEM3Embeddings实现

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        # 确保模型已加载
        self._ensure_model_loaded()

        if self.memory_monitor:
            self.log_memory_usage("开始嵌入")
            
        # 周期性缓存清理
        self._cleanup_counter += 1
        if self._cleanup_counter >= self._periodic_cleanup_interval:
            current_time = time.time()
            # 每隔一定时间进行过期缓存清理
            if current_time - self._last_cache_cleanup > 300:  # 5分钟
                self._cleanup_expired_cache()
                self._last_cache_cleanup = current_time
            self._cleanup_counter = 0

        # ★★★ 优化点 1: 预处理和缓存检查在GPU计算之外 ★★★
        embeddings = [None] * len(texts)
        texts_to_embed = []
        indices_to_embed = []

        # 预处理和缓存查找
        for i, text in enumerate(texts):
            processed_text = self._preprocess_text(text)
            if not processed_text:
                embeddings[i] = [0.0] * self.get_embedding_dimension()
                continue

            # 检查缓存
            cached_embedding = self._get_cached_embedding(processed_text)
            if cached_embedding is not None:
                embeddings[i] = cached_embedding.tolist()
            else:
                texts_to_embed.append(processed_text)
                indices_to_embed.append(i)

        # 如果有需要嵌入的文本，进行批处理
        if texts_to_embed:
            try:
                # ★★★ 关键优化：使用torch.no_grad()减少显存占用 ★★★
                with torch.no_grad():
                    logger.debug(f"正在嵌入 {len(texts_to_embed)} 个新文本")
                    
                    new_embeddings_list = []
                    should_cleanup = False
                    batch_count = 0

                    # 批处理嵌入
                    for batch in batch_process(texts_to_embed, self.batch_size):
                        batch_count += 1
                        
                        try:
                            # 生成嵌入向量
                            batch_output = self.model.encode(
                                batch,
                                batch_size=len(batch),
                                max_length=self.max_length,
                                return_dense=True,
                                return_sparse=False,
                                return_colbert_vecs=False,
                            )
                            
                            # 提取密集嵌入向量
                            batch_embeddings = batch_output["dense_vecs"]
                            
                            # 立即转换为numpy数组并释放GPU张量
                            if hasattr(batch_embeddings, 'cpu'):
                                batch_embeddings = batch_embeddings.cpu().numpy()
                            elif hasattr(batch_embeddings, 'detach'):
                                batch_embeddings = batch_embeddings.detach().cpu().numpy()
                            
                            # 确保是numpy数组格式
                            batch_embeddings = np.array(batch_embeddings, dtype=np.float32)
                            
                            # 手动标准化（如果启用）
                            if self.normalize_embeddings:
                                # 使用 L2 标准化
                                batch_embeddings = normalize(batch_embeddings, norm='l2', axis=1)
                            
                            new_embeddings_list.extend(batch_embeddings)
                            
                            # ★★★ 新增：立即清理临时变量和同步GPU ★★★
                            del batch_embeddings
                            gc.collect()
                            if torch.cuda.is_available() and self.device == "cuda":
                                torch.cuda.empty_cache()
                                torch.cuda.synchronize()  # 确保GPU操作完成
                            
                        except Exception as batch_error:
                            logger.error(f"批处理嵌入失败: {batch_error}")
                            raise

                        # 智能清理策略
                        if self.auto_cleanup:
                            # 每处理固定间隔进行清理
                            if batch_count % self.batch_cleanup_interval == 0:
                                should_cleanup = True
                            
                            # 如果启用监控，也基于显存使用率进行清理
                            elif self.memory_monitor and torch.cuda.is_available() and self.device == "cuda":
                                try:
                                    # ★★★ 新增：同步后再检查显存 ★★★
                                    torch.cuda.synchronize()
                                    memory_info = self.get_memory_info()
                                    usage_percent = memory_info.get("memory_usage_percent", 0)
                                    if usage_percent > self.cleanup_threshold * 0.8:  # 提前清理阈值
                                        should_cleanup = True
                                except:
                                    pass  # 忽略监控错误，继续处理
                        
                        if should_cleanup:
                            gc.collect()
                            if torch.cuda.is_available() and self.device == "cuda":
                                torch.cuda.empty_cache()
                                torch.cuda.synchronize()  # 确保清理操作完成
                            logger.debug(f"批处理清理 - 批次 {batch_count}")

                    # ★★★ 新增：最终同步确保所有GPU操作完成 ★★★
                    if torch.cuda.is_available() and self.device == "cuda":
                        torch.cuda.synchronize()

                # 将新生成的嵌入填充回原列表
                for i, embedding in enumerate(new_embeddings_list):
                    original_index = indices_to_embed[i]
                    embeddings[original_index] = embedding
                    self._cache_embedding(texts_to_embed[i], embedding)

                if len(texts_to_embed) > 0:
                    self._save_cache()

            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                # 异常情况下也要清理显存
                if self.auto_cleanup:
                    self.synchronize_and_cleanup()
                raise
            finally:
                # 任务完成后自动清理
                if self.auto_cleanup:
                    self.synchronize_and_cleanup()
                    logger.debug("任务完成后自动清理执行")
                    
                if self.memory_monitor:
                    # ★★★ 延迟监控确保清理生效 ★★★
                    if torch.cuda.is_available() and self.device == "cuda":
                        torch.cuda.synchronize()
                    self.log_memory_usage("嵌入完成")

        # ★★★ 优化点 2: 对整个矩阵进行 tolist() 转换 ★★★
        # 首先将列表（其中可能混合了np数组和列表）统一转换为一个大的np数组
        final_embeddings_np = np.array(embeddings, dtype=np.float32)
        return final_embeddings_np.tolist()

    def embed_query(self, text: str) -> List[float]:
        """
        嵌入查询文本

        Args:
            text: 查询文本

        Returns:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    def get_embedding_dimension(self) -> int:
        """
        获取嵌入维度

        Returns:
            嵌入维度
        """
        return 1024  # BGE-M3固定维度

    def health_check(self) -> bool:
        """
        健康检查

        Returns:
            服务是否健康
        """
        try:
            if self.model is None:
                return False

            # 测试嵌入
            test_result = self.embed_query("health check")
            
            # 检查显存状态（如果启用监控）
            if self.memory_monitor and torch.cuda.is_available() and self.device == "cuda":
                memory_info = self.get_memory_info()
                if memory_info["memory_usage_percent"] > 95:  # 显存使用率过高
                    logger.warning("健康检查: GPU显存使用率过高")
                    return False
            
            return len(test_result) > 0
        except Exception as e:
            logger.error(f"Embedding service health check failed: {e}")
            return False

    def check_memory_leak(self) -> Dict[str, Any]:
        """
        检查潜在的显存泄漏
        
        Returns:
            泄漏检查结果
        """
        result = {
            "leak_detected": False,
            "warning_level": "normal",
            "recommendations": []
        }
        
        try:
            if not (torch.cuda.is_available() and self.device == "cuda"):
                result["recommendations"].append("CPU模式，无需检查显存泄漏")
                return result
                
            memory_info = self.get_memory_info()
            usage_percent = memory_info.get("memory_usage_percent", 0)
            allocated = memory_info.get("allocated", 0)
            reserved = memory_info.get("reserved", 0)
            
            # 检查显存使用率
            if usage_percent > 95:
                result["leak_detected"] = True
                result["warning_level"] = "critical"
                result["recommendations"].extend([
                    "显存使用率过高，疑似泄漏",
                    "建议立即执行强制清理",
                    "考虑重启服务"
                ])
            elif usage_percent > self.cleanup_threshold:
                result["warning_level"] = "warning"
                result["recommendations"].extend([
                    "显存使用率较高，需要关注",
                    "建议执行清理操作"
                ])
            
            # 检查分配与保留的比率
            if allocated > 0 and reserved > 0:
                fragmentation_ratio = (reserved - allocated) / reserved
                if fragmentation_ratio > 0.5:
                    result["leak_detected"] = True
                    result["recommendations"].append("显存碎片化严重，建议重载模型")
            
            # 检查最大分配内存
            max_allocated = memory_info.get("max_allocated", 0)
            if max_allocated > allocated * 2:
                result["recommendations"].append("历史峰值显存较高，注意监控")
            
            result.update({
                "current_usage_percent": usage_percent,
                "allocated_gb": allocated,
                "reserved_gb": reserved,
                "fragmentation_ratio": fragmentation_ratio if allocated > 0 and reserved > 0 else 0
            })
            
        except Exception as e:
            result["error"] = str(e)
            result["recommendations"].append("显存检查失败，请手动检查")
        
        return result

    def _start_model_idle_checker(self):
        """启动模型空闲检查器"""
        if self._model_idle_checker_thread is not None:
            logger.debug("模型空闲检查器已在运行")
            return

        logger.info(f"启动模型空闲检查器，检查间隔: {self.model_check_interval}秒")
        self._model_idle_checker_stop_event.clear()
        self._model_idle_checker_thread = threading.Thread(
            target=self._model_idle_checker_loop,
            name="ModelIdleChecker",
            daemon=True
        )
        self._model_idle_checker_thread.start()

    def _stop_model_idle_checker(self):
        """停止模型空闲检查器"""
        if self._model_idle_checker_thread is None:
            return

        logger.info("停止模型空闲检查器")
        self._model_idle_checker_stop_event.set()

        # 等待线程结束，最多等待5秒
        if self._model_idle_checker_thread.is_alive():
            self._model_idle_checker_thread.join(timeout=5.0)

        self._model_idle_checker_thread = None

    def _model_idle_checker_loop(self):
        """模型空闲检查器主循环"""
        logger.debug("模型空闲检查器开始运行")

        while not self._model_idle_checker_stop_event.is_set():
            try:
                # 等待检查间隔，如果收到停止信号则退出
                if self._model_idle_checker_stop_event.wait(self.model_check_interval):
                    break

                # 检查模型是否应该卸载
                if self._should_unload_model():
                    logger.info("检测到模型空闲，准备卸载")
                    self._unload_model()

            except Exception as e:
                logger.error(f"模型空闲检查器发生错误: {e}")
                # 发生错误时等待一段时间再继续
                time.sleep(60)

        logger.debug("模型空闲检查器已停止")

    def _should_unload_model(self) -> bool:
        """判断是否应该卸载模型"""
        with self._model_lock:
            # 如果模型未加载，无需卸载
            if self.model is None:
                return False

            # 检查空闲时间
            current_time = time.time()
            idle_time = current_time - self._model_last_used

            if idle_time < self.model_idle_timeout:
                return False

            # 检查内存压力（CPU模式）
            if self.device == "cpu":
                try:
                    import psutil
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > 80:  # 内存使用超过80%
                        logger.debug(f"内存压力较高({memory_percent:.1f}%)，触发模型卸载")
                        return True
                except ImportError:
                    pass

            # 基于空闲时间决定
            logger.debug(f"模型空闲时间: {idle_time:.1f}秒，超时阈值: {self.model_idle_timeout}秒")
            return True

    def _unload_model(self):
        """安全卸载模型"""
        with self._model_lock:
            if self.model is None:
                logger.debug("模型已经卸载")
                return

            try:
                logger.info("开始卸载模型以释放内存")

                # 记录卸载前的内存状态
                if self.memory_monitor:
                    self.log_memory_usage("模型卸载前")

                # 清理模型
                del self.model
                self.model = None

                # 强制垃圾回收
                gc.collect()

                # CPU模式额外清理
                if self.device == "cpu":
                    # 多轮垃圾回收
                    import time as time_module
                    for _ in range(3):
                        gc.collect()
                        time_module.sleep(0.1)
                else:
                    # GPU模式清理CUDA缓存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                # 记录卸载后的内存状态
                if self.memory_monitor:
                    self.log_memory_usage("模型卸载后")

                logger.info("✅ 模型卸载完成")

            except Exception as e:
                logger.error(f"模型卸载失败: {e}")

    def _ensure_model_loaded(self):
        """确保模型已加载"""
        with self._model_lock:
            if self.model is None:
                logger.info("模型未加载，开始重新加载")
                self._load_model()

            # 更新最后使用时间
            self._model_last_used = time.time()
            self._model_usage_count += 1
