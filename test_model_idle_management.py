#!/usr/bin/env python3
"""
测试模型空闲检查和智能卸载功能
"""

import os
import sys
import time
import gc
from pathlib import Path

# 强制使用CPU模式
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"


def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # MB
    except ImportError:
        return 0


def test_model_idle_management():
    """测试模型空闲管理功能"""
    print("🚀 测试模型空闲检查和智能卸载功能")
    print("=" * 60)
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        # 创建服务实例，设置较短的空闲超时时间用于测试
        print("📝 创建BGE嵌入服务实例...")
        service = BGEEmbeddingService(
            model_name=str(project_root / "models" / "bge-m3-safetensors-only"),
            device="cpu",
            memory_monitor=True,
            auto_cleanup=True,
            model_idle_timeout=10,  # 10秒超时
            model_check_interval=5   # 5秒检查一次
        )
        
        print(f"✅ 服务创建成功")
        print(f"模型空闲超时: {service.model_idle_timeout}秒")
        print(f"检查间隔: {service.model_check_interval}秒")
        print(f"懒加载: {service.lazy_loading}")
        print(f"空闲检查器线程: {service._model_idle_checker_thread}")
        print(f"空闲检查器是否运行: {service._model_idle_checker_thread.is_alive() if service._model_idle_checker_thread else False}")
        
        # 记录初始内存
        initial_memory = get_memory_usage()
        print(f"📊 初始内存使用: {initial_memory:.1f}MB")
        
        # 测试1: 验证模型懒加载
        print("\n🔄 测试1: 验证模型懒加载")
        print(f"模型是否已加载: {service.model is not None}")
        
        # 执行一次嵌入操作，触发模型加载
        print("执行嵌入操作，触发模型加载...")
        test_texts = ["这是一个测试文本", "另一个测试文本"]
        embeddings = service.embed_documents(test_texts)
        
        after_load_memory = get_memory_usage()
        print(f"✅ 嵌入完成，维度: {len(embeddings[0])}")
        print(f"📊 模型加载后内存: {after_load_memory:.1f}MB")
        print(f"📈 内存增长: {after_load_memory - initial_memory:.1f}MB")
        print(f"模型是否已加载: {service.model is not None}")
        
        # 测试2: 等待模型空闲检查器工作
        print(f"\n🔄 测试2: 等待模型空闲检查器工作")
        print(f"等待 {service.model_idle_timeout + service.model_check_interval + 5} 秒...")
        
        # 等待足够长的时间让模型被卸载
        wait_time = service.model_idle_timeout + service.model_check_interval + 5
        for i in range(wait_time):
            time.sleep(1)
            if i % 5 == 0:
                current_memory = get_memory_usage()
                model_loaded = service.model is not None
                print(f"⏰ {i}秒 - 内存: {current_memory:.1f}MB, 模型加载: {model_loaded}")
        
        # 检查模型是否被卸载
        final_memory = get_memory_usage()
        model_loaded = service.model is not None
        
        print(f"\n📊 最终状态:")
        print(f"模型是否已加载: {model_loaded}")
        print(f"最终内存使用: {final_memory:.1f}MB")
        print(f"内存释放: {after_load_memory - final_memory:.1f}MB")
        
        # 测试3: 验证模型重新加载
        print(f"\n🔄 测试3: 验证模型重新加载")
        print("再次执行嵌入操作...")
        embeddings2 = service.embed_documents(test_texts)
        
        reload_memory = get_memory_usage()
        print(f"✅ 重新嵌入完成，维度: {len(embeddings2[0])}")
        print(f"📊 重新加载后内存: {reload_memory:.1f}MB")
        print(f"模型是否已加载: {service.model is not None}")
        
        # 验证嵌入结果一致性
        import numpy as np
        similarity = np.dot(embeddings[0], embeddings2[0])
        print(f"🔍 嵌入结果相似度: {similarity:.4f} (应该接近1.0)")
        
        # 清理资源
        print(f"\n🧹 清理资源...")
        service.cleanup()
        
        cleanup_memory = get_memory_usage()
        print(f"📊 清理后内存: {cleanup_memory:.1f}MB")
        print(f"总内存释放: {after_load_memory - cleanup_memory:.1f}MB")
        
        # 评估测试结果
        print(f"\n📋 测试结果评估:")
        success_criteria = []
        
        # 1. 模型应该被成功卸载
        if not model_loaded:
            success_criteria.append("✅ 模型空闲卸载成功")
        else:
            success_criteria.append("❌ 模型未被卸载")
            
        # 2. 内存应该有显著释放
        memory_released = after_load_memory - final_memory
        if memory_released > 100:  # 至少释放100MB
            success_criteria.append(f"✅ 内存释放显著: {memory_released:.1f}MB")
        else:
            success_criteria.append(f"⚠️  内存释放有限: {memory_released:.1f}MB")
            
        # 3. 嵌入结果应该一致
        if similarity > 0.99:
            success_criteria.append("✅ 嵌入结果一致性良好")
        else:
            success_criteria.append("⚠️  嵌入结果一致性待验证")
        
        for criterion in success_criteria:
            print(criterion)
            
        return all("✅" in criterion for criterion in success_criteria)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 模型空闲管理功能测试")
    print(f"项目根目录: {project_root}")
    print("=" * 60)
    
    # 检查模型文件是否存在
    model_path = project_root / "models" / "bge-m3-safetensors-only"
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 python models/download_models.py 下载模型")
        return 1
    
    # 执行测试
    success = test_model_idle_management()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 模型空闲管理功能测试通过！")
        print("✅ 模型空闲检查和智能卸载功能正常工作")
        return 0
    else:
        print("❌ 模型空闲管理功能测试失败")
        print("请检查相关功能实现")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
