#!/usr/bin/env python3
"""
深入分析内存使用情况，找出内存释放效果不理想的原因
"""

import os
import sys
import time
import gc
from pathlib import Path

# 强制使用CPU模式
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"


def get_detailed_memory_info():
    """获取详细的内存使用信息"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        # 获取系统内存信息
        system_memory = psutil.virtual_memory()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # 物理内存
            "vms_mb": memory_info.vms / 1024 / 1024,  # 虚拟内存
            "percent": process.memory_percent(),       # 内存占用百分比
            "system_available_mb": system_memory.available / 1024 / 1024,
            "system_used_percent": system_memory.percent
        }
    except ImportError:
        return {"error": "psutil not available"}


def analyze_python_objects():
    """分析Python对象的内存使用"""
    try:
        import tracemalloc
        
        # 启动内存跟踪
        tracemalloc.start()
        
        # 获取当前内存快照
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        print("\n📊 Python对象内存使用TOP 10:")
        for index, stat in enumerate(top_stats[:10], 1):
            print(f"{index:2d}. {stat}")
            
        return snapshot
        
    except Exception as e:
        print(f"内存跟踪失败: {e}")
        return None


def analyze_gc_objects():
    """分析垃圾回收对象"""
    try:
        # 获取垃圾回收统计
        gc_stats = gc.get_stats()
        print(f"\n🗑️ 垃圾回收统计: {gc_stats}")
        
        # 获取所有对象计数
        all_objects = gc.get_objects()
        print(f"总对象数: {len(all_objects)}")
        
        # 按类型统计对象
        type_counts = {}
        for obj in all_objects:
            obj_type = type(obj).__name__
            type_counts[obj_type] = type_counts.get(obj_type, 0) + 1
        
        # 显示最多的对象类型
        print("\n📈 对象类型统计TOP 10:")
        sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (obj_type, count) in enumerate(sorted_types[:10], 1):
            print(f"{i:2d}. {obj_type}: {count}")
            
        return type_counts
        
    except Exception as e:
        print(f"垃圾回收分析失败: {e}")
        return {}


def test_memory_behavior():
    """测试内存行为"""
    print("🔍 深入分析内存使用情况")
    print("=" * 60)
    
    # 初始状态
    print("📊 初始状态:")
    initial_memory = get_detailed_memory_info()
    print(f"物理内存: {initial_memory['rss_mb']:.1f}MB")
    print(f"虚拟内存: {initial_memory['vms_mb']:.1f}MB")
    print(f"内存占用: {initial_memory['percent']:.1f}%")
    
    initial_snapshot = analyze_python_objects()
    initial_gc = analyze_gc_objects()
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        print(f"\n🔄 创建BGE嵌入服务...")
        service = BGEEmbeddingService(
            model_name=str(project_root / "models" / "bge-m3-safetensors-only"),
            device="cpu",
            memory_monitor=True,
            auto_cleanup=True,
            model_idle_timeout=5,  # 5秒超时
            model_check_interval=3   # 3秒检查一次
        )
        
        # 服务创建后
        print(f"\n📊 服务创建后:")
        after_service_memory = get_detailed_memory_info()
        print(f"物理内存: {after_service_memory['rss_mb']:.1f}MB")
        print(f"内存增长: {after_service_memory['rss_mb'] - initial_memory['rss_mb']:.1f}MB")
        
        # 执行嵌入操作
        print(f"\n🔄 执行嵌入操作...")
        test_texts = ["这是一个测试文本", "另一个测试文本", "第三个测试文本"]
        embeddings = service.embed_documents(test_texts)
        
        # 模型加载后
        print(f"\n📊 模型加载后:")
        after_embed_memory = get_detailed_memory_info()
        print(f"物理内存: {after_embed_memory['rss_mb']:.1f}MB")
        print(f"内存增长: {after_embed_memory['rss_mb'] - after_service_memory['rss_mb']:.1f}MB")
        print(f"嵌入维度: {len(embeddings[0])}")
        
        after_embed_snapshot = analyze_python_objects()
        after_embed_gc = analyze_gc_objects()
        
        # 等待模型卸载
        print(f"\n⏰ 等待模型自动卸载...")
        wait_time = service.model_idle_timeout + service.model_check_interval + 2
        for i in range(wait_time):
            time.sleep(1)
            if i % 2 == 0:
                current_memory = get_detailed_memory_info()
                model_loaded = service.model is not None
                print(f"  {i}秒 - 内存: {current_memory['rss_mb']:.1f}MB, 模型: {model_loaded}")
        
        # 模型卸载后
        print(f"\n📊 模型卸载后:")
        after_unload_memory = get_detailed_memory_info()
        print(f"物理内存: {after_unload_memory['rss_mb']:.1f}MB")
        print(f"内存释放: {after_embed_memory['rss_mb'] - after_unload_memory['rss_mb']:.1f}MB")
        print(f"模型状态: {service.model is not None}")
        
        after_unload_snapshot = analyze_python_objects()
        after_unload_gc = analyze_gc_objects()
        
        # 手动强制清理
        print(f"\n🧹 手动强制清理...")
        
        # 多轮垃圾回收
        for i in range(5):
            collected = gc.collect()
            print(f"  垃圾回收第{i+1}轮: {collected} 个对象")
        
        # 清理后
        after_manual_cleanup_memory = get_detailed_memory_info()
        print(f"\n📊 手动清理后:")
        print(f"物理内存: {after_manual_cleanup_memory['rss_mb']:.1f}MB")
        print(f"额外释放: {after_unload_memory['rss_mb'] - after_manual_cleanup_memory['rss_mb']:.1f}MB")
        
        # 分析内存差异
        print(f"\n🔍 内存使用分析:")
        print(f"服务创建开销: {after_service_memory['rss_mb'] - initial_memory['rss_mb']:.1f}MB")
        print(f"模型加载开销: {after_embed_memory['rss_mb'] - after_service_memory['rss_mb']:.1f}MB")
        print(f"自动卸载释放: {after_embed_memory['rss_mb'] - after_unload_memory['rss_mb']:.1f}MB")
        print(f"手动清理释放: {after_unload_memory['rss_mb'] - after_manual_cleanup_memory['rss_mb']:.1f}MB")
        print(f"总体内存增长: {after_manual_cleanup_memory['rss_mb'] - initial_memory['rss_mb']:.1f}MB")
        
        # 对象数量变化分析
        print(f"\n📈 对象数量变化:")
        if initial_gc and after_unload_gc:
            for obj_type in ['dict', 'list', 'tuple', 'str', 'function']:
                initial_count = initial_gc.get(obj_type, 0)
                final_count = after_unload_gc.get(obj_type, 0)
                change = final_count - initial_count
                print(f"  {obj_type}: {initial_count} -> {final_count} ({change:+d})")
        
        # 清理服务
        service.cleanup()
        
        # 最终状态
        final_memory = get_detailed_memory_info()
        print(f"\n📊 最终状态:")
        print(f"物理内存: {final_memory['rss_mb']:.1f}MB")
        print(f"相对初始状态: {final_memory['rss_mb'] - initial_memory['rss_mb']:.1f}MB")
        
        return {
            "initial": initial_memory,
            "after_service": after_service_memory,
            "after_embed": after_embed_memory,
            "after_unload": after_unload_memory,
            "after_manual_cleanup": after_manual_cleanup_memory,
            "final": final_memory
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    print("🔍 内存使用深度分析")
    print(f"项目根目录: {project_root}")
    print("=" * 60)
    
    # 检查模型文件
    model_path = project_root / "models" / "bge-m3-safetensors-only"
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return 1
    
    # 执行分析
    results = test_memory_behavior()
    
    if results:
        print("\n" + "=" * 60)
        print("📋 分析总结")
        print("=" * 60)
        
        # 计算关键指标
        service_overhead = results["after_service"]["rss_mb"] - results["initial"]["rss_mb"]
        model_overhead = results["after_embed"]["rss_mb"] - results["after_service"]["rss_mb"]
        auto_release = results["after_embed"]["rss_mb"] - results["after_unload"]["rss_mb"]
        manual_release = results["after_unload"]["rss_mb"] - results["after_manual_cleanup"]["rss_mb"]
        
        print(f"🏗️  服务创建开销: {service_overhead:.1f}MB")
        print(f"🧠 模型加载开销: {model_overhead:.1f}MB")
        print(f"🤖 自动卸载释放: {auto_release:.1f}MB")
        print(f"🧹 手动清理释放: {manual_release:.1f}MB")
        print(f"📊 释放效率: {(auto_release / model_overhead * 100):.1f}%")
        
        if auto_release < model_overhead * 0.5:
            print("\n⚠️  问题分析:")
            print("- 模型卸载后内存释放不充分")
            print("- 可能存在内存泄漏或引用未完全清理")
            print("- 建议检查模型对象的引用关系")
            print("- 考虑更积极的内存管理策略")
        
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
