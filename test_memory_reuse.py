#!/usr/bin/env python3
"""
测试内存重用效果 - 验证模型卸载后内存是否能被有效重用
"""

import os
import sys
import time
import gc
from pathlib import Path

# 强制使用CPU模式
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"


def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # MB
    except ImportError:
        return 0


def test_memory_reuse():
    """测试内存重用效果"""
    print("🔄 测试内存重用效果")
    print("=" * 60)
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        # 记录初始内存
        initial_memory = get_memory_usage()
        print(f"📊 初始内存: {initial_memory:.1f}MB")
        
        # 第一轮：加载模型并使用
        print(f"\n🔄 第一轮：创建服务并加载模型")
        service1 = BGEEmbeddingService(
            model_name=str(project_root / "models" / "bge-m3-safetensors-only"),
            device="cpu",
            memory_monitor=True,
            auto_cleanup=True,
            model_idle_timeout=5,
            model_check_interval=3
        )
        
        after_service1_memory = get_memory_usage()
        print(f"📊 服务1创建后: {after_service1_memory:.1f}MB (+{after_service1_memory - initial_memory:.1f}MB)")
        
        # 执行嵌入操作
        test_texts = ["第一轮测试文本", "另一个测试文本"]
        embeddings1 = service1.embed_documents(test_texts)
        
        after_embed1_memory = get_memory_usage()
        print(f"📊 第一轮嵌入后: {after_embed1_memory:.1f}MB (+{after_embed1_memory - after_service1_memory:.1f}MB)")
        print(f"✅ 第一轮嵌入完成，维度: {len(embeddings1[0])}")
        
        # 等待模型自动卸载
        print(f"\n⏰ 等待第一轮模型自动卸载...")
        wait_time = service1.model_idle_timeout + service1.model_check_interval + 2
        for i in range(wait_time):
            time.sleep(1)
            if i % 2 == 0:
                current_memory = get_memory_usage()
                model_loaded = service1.model is not None
                print(f"  {i}秒 - 内存: {current_memory:.1f}MB, 模型: {model_loaded}")
        
        after_unload1_memory = get_memory_usage()
        print(f"📊 第一轮卸载后: {after_unload1_memory:.1f}MB")
        print(f"模型1状态: {service1.model is not None}")
        
        # 第二轮：创建新服务并加载模型
        print(f"\n🔄 第二轮：创建新服务并加载模型")
        service2 = BGEEmbeddingService(
            model_name=str(project_root / "models" / "bge-m3-safetensors-only"),
            device="cpu",
            memory_monitor=True,
            auto_cleanup=True,
            model_idle_timeout=5,
            model_check_interval=3
        )
        
        after_service2_memory = get_memory_usage()
        print(f"📊 服务2创建后: {after_service2_memory:.1f}MB (+{after_service2_memory - after_unload1_memory:.1f}MB)")
        
        # 执行嵌入操作
        test_texts2 = ["第二轮测试文本", "又一个测试文本"]
        embeddings2 = service2.embed_documents(test_texts2)
        
        after_embed2_memory = get_memory_usage()
        print(f"📊 第二轮嵌入后: {after_embed2_memory:.1f}MB (+{after_embed2_memory - after_service2_memory:.1f}MB)")
        print(f"✅ 第二轮嵌入完成，维度: {len(embeddings2[0])}")
        
        # 第三轮：重用第一个服务
        print(f"\n🔄 第三轮：重用第一个服务")
        test_texts3 = ["第三轮测试文本", "最后一个测试文本"]
        embeddings3 = service1.embed_documents(test_texts3)
        
        after_embed3_memory = get_memory_usage()
        print(f"📊 第三轮嵌入后: {after_embed3_memory:.1f}MB (+{after_embed3_memory - after_embed2_memory:.1f}MB)")
        print(f"✅ 第三轮嵌入完成，维度: {len(embeddings3[0])}")
        
        # 分析内存重用效果
        print(f"\n📊 内存重用分析:")
        first_load_cost = after_embed1_memory - after_service1_memory
        second_load_cost = after_embed2_memory - after_service2_memory
        third_load_cost = after_embed3_memory - after_embed2_memory
        
        print(f"第一次模型加载成本: {first_load_cost:.1f}MB")
        print(f"第二次模型加载成本: {second_load_cost:.1f}MB")
        print(f"第三次模型加载成本: {third_load_cost:.1f}MB")
        
        # 计算重用效率
        if first_load_cost > 0:
            second_efficiency = (1 - second_load_cost / first_load_cost) * 100
            third_efficiency = (1 - third_load_cost / first_load_cost) * 100
            print(f"第二次加载效率: {second_efficiency:.1f}% (内存节省)")
            print(f"第三次加载效率: {third_efficiency:.1f}% (内存节省)")
        
        # 验证嵌入质量一致性
        import numpy as np
        similarity_1_2 = np.dot(embeddings1[0], embeddings2[0])
        similarity_1_3 = np.dot(embeddings1[0], embeddings3[0])
        print(f"\n🔍 嵌入质量一致性:")
        print(f"第1轮 vs 第2轮相似度: {similarity_1_2:.4f}")
        print(f"第1轮 vs 第3轮相似度: {similarity_1_3:.4f}")
        
        # 清理资源
        print(f"\n🧹 清理所有资源...")
        service1.cleanup()
        service2.cleanup()
        
        final_memory = get_memory_usage()
        print(f"📊 最终内存: {final_memory:.1f}MB")
        print(f"相对初始状态: {final_memory - initial_memory:.1f}MB")
        
        # 评估结果
        print(f"\n📋 测试结果评估:")
        success_criteria = []
        
        # 1. 内存重用效果
        if second_load_cost < first_load_cost * 0.8:
            success_criteria.append("✅ 内存重用效果良好")
        else:
            success_criteria.append("⚠️  内存重用效果有限")
            
        # 2. 嵌入质量一致性
        if similarity_1_2 > 0.95 and similarity_1_3 > 0.95:
            success_criteria.append("✅ 嵌入质量一致性良好")
        else:
            success_criteria.append("⚠️  嵌入质量一致性待验证")
            
        # 3. 总体内存增长控制
        total_growth = final_memory - initial_memory
        if total_growth < 1000:  # 小于1GB
            success_criteria.append("✅ 总体内存增长可控")
        else:
            success_criteria.append("⚠️  总体内存增长较大")
        
        for criterion in success_criteria:
            print(criterion)
            
        return all("✅" in criterion for criterion in success_criteria)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 内存重用效果测试")
    print(f"项目根目录: {project_root}")
    print("=" * 60)
    
    # 检查模型文件
    model_path = project_root / "models" / "bge-m3-safetensors-only"
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return 1
    
    # 执行测试
    success = test_memory_reuse()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 内存重用测试通过！")
        print("✅ 模型空闲卸载和内存重用功能正常工作")
        print("📝 说明：虽然内存不会立即返回给操作系统，")
        print("      但已释放的内存可以被有效重用，这是正常的内存管理行为。")
        return 0
    else:
        print("❌ 内存重用测试失败")
        print("请检查内存管理策略")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
