#!/usr/bin/env python3
"""
CPU模式内存优化最终验证测试
验证是否达到了从2.93GB降低到<1GB的目标
"""

import os
import sys
import time
import gc
from pathlib import Path

# 强制使用CPU模式
os.environ["CUDA_VISIBLE_DEVICES"] = ""

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 禁用遥测
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"


def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # MB
    except ImportError:
        return 0


def simulate_production_workload():
    """模拟生产环境工作负载"""
    print("🏭 模拟生产环境工作负载")
    print("=" * 60)
    
    try:
        from services.vector_service.core.embeddings import BGEEmbeddingService
        
        # 记录初始内存
        initial_memory = get_memory_usage()
        print(f"📊 初始内存: {initial_memory:.1f}MB")
        
        # 创建服务（模拟向量服务启动）
        print(f"\n🚀 启动向量服务...")
        service = BGEEmbeddingService(
            model_name=str(project_root / "models" / "bge-m3-safetensors-only"),
            device="cpu",
            memory_monitor=True,
            auto_cleanup=True,
            model_idle_timeout=30,  # 30秒空闲超时（生产环境设置）
            model_check_interval=10   # 10秒检查一次
        )
        
        after_service_memory = get_memory_usage()
        print(f"📊 服务启动后: {after_service_memory:.1f}MB (+{after_service_memory - initial_memory:.1f}MB)")
        
        # 模拟多轮向量化任务
        total_documents = 0
        peak_memory = after_service_memory
        
        for round_num in range(5):
            print(f"\n🔄 第{round_num + 1}轮向量化任务")
            
            # 模拟不同大小的文档批次
            if round_num == 0:
                # 第一轮：小批次
                test_texts = [f"第{round_num + 1}轮测试文档{i}" for i in range(3)]
            elif round_num == 1:
                # 第二轮：中等批次
                test_texts = [f"第{round_num + 1}轮测试文档{i}" for i in range(10)]
            elif round_num == 2:
                # 第三轮：大批次
                test_texts = [f"第{round_num + 1}轮测试文档{i}" for i in range(20)]
            else:
                # 后续轮次：随机大小
                import random
                batch_size = random.randint(5, 15)
                test_texts = [f"第{round_num + 1}轮测试文档{i}" for i in range(batch_size)]
            
            # 执行向量化
            start_time = time.time()
            embeddings = service.embed_documents(test_texts)
            end_time = time.time()
            
            current_memory = get_memory_usage()
            peak_memory = max(peak_memory, current_memory)
            total_documents += len(test_texts)
            
            print(f"  📄 处理文档: {len(test_texts)}个")
            print(f"  ⏱️  处理时间: {end_time - start_time:.2f}秒")
            print(f"  📊 当前内存: {current_memory:.1f}MB")
            print(f"  🧠 模型状态: {'已加载' if service.model is not None else '未加载'}")
            
            # 模拟任务间隔（让模型有机会被卸载）
            if round_num < 4:  # 最后一轮不等待
                print(f"  ⏸️  任务间隔等待...")
                interval_time = 35 if round_num == 1 else 15  # 第二轮等待更长时间
                for i in range(interval_time):
                    time.sleep(1)
                    if i % 5 == 0:
                        interval_memory = get_memory_usage()
                        model_loaded = service.model is not None
                        print(f"    {i}秒 - 内存: {interval_memory:.1f}MB, 模型: {'已加载' if model_loaded else '未加载'}")
        
        # 最终状态
        final_memory = get_memory_usage()
        print(f"\n📊 工作负载完成:")
        print(f"总处理文档: {total_documents}个")
        print(f"峰值内存: {peak_memory:.1f}MB")
        print(f"最终内存: {final_memory:.1f}MB")
        print(f"内存增长: {final_memory - initial_memory:.1f}MB")
        print(f"模型状态: {'已加载' if service.model is not None else '未加载'}")
        
        # 获取优化统计
        stats = service.get_memory_optimization_stats()
        print(f"\n📈 内存优化统计:")
        print(f"  模型使用次数: {stats['model_usage_count']}")
        print(f"  缓存条目数: {stats['cache_entries']}")
        print(f"  缓存内存: {stats['cache_memory_mb']:.2f}MB")
        print(f"  空闲检查器运行: {stats['idle_checker_running']}")
        
        # 清理资源
        print(f"\n🧹 清理服务资源...")
        service.cleanup()
        
        cleanup_memory = get_memory_usage()
        print(f"📊 清理后内存: {cleanup_memory:.1f}MB")
        
        # 评估优化效果
        print(f"\n📋 优化效果评估:")
        
        # 目标：从2.93GB降低到<1GB
        target_memory_gb = 1.0
        peak_memory_gb = peak_memory / 1024
        final_memory_gb = final_memory / 1024
        
        print(f"🎯 优化目标: <{target_memory_gb:.1f}GB")
        print(f"📊 峰值内存: {peak_memory_gb:.2f}GB")
        print(f"📊 最终内存: {final_memory_gb:.2f}GB")
        
        success_criteria = []
        
        # 1. 峰值内存控制
        if peak_memory_gb < target_memory_gb:
            success_criteria.append("✅ 峰值内存控制达标")
        else:
            success_criteria.append(f"⚠️  峰值内存超标: {peak_memory_gb:.2f}GB > {target_memory_gb:.1f}GB")
        
        # 2. 最终内存控制
        if final_memory_gb < target_memory_gb:
            success_criteria.append("✅ 最终内存控制达标")
        else:
            success_criteria.append(f"⚠️  最终内存超标: {final_memory_gb:.2f}GB > {target_memory_gb:.1f}GB")
        
        # 3. 模型自动卸载
        if not service.model:
            success_criteria.append("✅ 模型自动卸载正常")
        else:
            success_criteria.append("⚠️  模型未自动卸载")
        
        # 4. 内存增长控制
        memory_growth_gb = (final_memory - initial_memory) / 1024
        if memory_growth_gb < 0.8:  # 增长小于800MB
            success_criteria.append("✅ 内存增长控制良好")
        else:
            success_criteria.append(f"⚠️  内存增长较大: {memory_growth_gb:.2f}GB")
        
        for criterion in success_criteria:
            print(criterion)
        
        # 计算优化效果
        original_memory_gb = 2.93  # 原始问题中的内存使用
        improvement_percent = (1 - peak_memory_gb / original_memory_gb) * 100
        print(f"\n🎉 内存优化效果: {improvement_percent:.1f}% (从{original_memory_gb:.2f}GB降至{peak_memory_gb:.2f}GB)")
        
        return all("✅" in criterion for criterion in success_criteria)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 CPU模式内存优化最终验证")
    print(f"项目根目录: {project_root}")
    print("=" * 60)
    
    # 检查模型文件
    model_path = project_root / "models" / "bge-m3-safetensors-only"
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return 1
    
    # 执行测试
    success = simulate_production_workload()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CPU模式内存优化验证通过！")
        print("✅ 成功实现了内存使用优化目标")
        print("✅ 模型空闲检查和智能卸载功能正常")
        print("✅ 内存重用机制有效工作")
        print("✅ 生产环境工作负载测试通过")
        print("\n📝 优化总结:")
        print("- 实现了模型空闲自动卸载")
        print("- 内存可以被有效重用")
        print("- 峰值内存使用控制在目标范围内")
        print("- 系统在长时间运行后内存稳定")
        return 0
    else:
        print("❌ CPU模式内存优化验证失败")
        print("部分优化目标未达成，需要进一步改进")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
