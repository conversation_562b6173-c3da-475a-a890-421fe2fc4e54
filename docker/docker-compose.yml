version: '3.8'

services:
  # ChromaDB 独立服务 (挂载vector_service的数据)
  chromadb:
    image: chromadb/chroma:latest
    container_name: deep-risk-chromadb
    ports:
      - "8001:8000"
    volumes:
      - ../cache/chroma_db:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - ANONYMIZED_TELEMETRY=False
    networks:
      - deep-risk-network
    restart: unless-stopped

  # 向量化服务 (GPU部署)
  vector-service:
    build:
      context: ..
      dockerfile: docker/vector-service.Dockerfile
    container_name: deep-risk-vector-service
    ports:
      - "8002:8000"
    volumes:
      - ../cache:/app/cache
      - ../models:/app/models
      - ../data:/app/data
    environment:
      - SERVICE_NAME=vector-service
      - SERVICE_PORT=8000
      - CHROMA_PERSIST_DIR=/app/cache/chroma_db
      - LOG_LEVEL=INFO
      - CUDA_VISIBLE_DEVICES=0  # 指定GPU设备
    depends_on:
      - chromadb
    networks:
      - deep-risk-network
    restart: unless-stopped
    # GPU支持 (需要nvidia-docker)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # 分析服务 (CPU部署)
  analysis-service:
    build:
      context: ..
      dockerfile: docker/analysis-service.Dockerfile
    container_name: deep-risk-analysis-service
    ports:
      - "8003:8000"
    volumes:
      - ../cache:/app/cache
      - ../prompts:/app/prompts
    environment:
      - SERVICE_NAME=analysis-service
      - SERVICE_PORT=8000
      - VECTOR_SERVICE_URL=http://vector-service:8000
      - CHROMA_HOST=chromadb
      - CHROMA_PORT=8000
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - LOG_LEVEL=INFO
    depends_on:
      - vector-service
      - chromadb
    networks:
      - deep-risk-network
    restart: unless-stopped

  # Redis (用于任务队列和缓存)
  redis:
    image: redis:7-alpine
    container_name: deep-risk-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - deep-risk-network
    restart: unless-stopped

networks:
  deep-risk-network:
    driver: bridge

volumes:
  redis_data:
